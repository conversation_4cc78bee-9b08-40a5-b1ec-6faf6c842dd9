package org.example.petcarebe;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.core.env.ConfigurableEnvironment;

@SpringBootApplication
public class PetCareBeApplication {

    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(PetCareBeApplication.class);
        app.addInitializers(applicationContext -> {
            ConfigurableEnvironment environment = applicationContext.getEnvironment();
        });
        app.run(args);
    }

}
