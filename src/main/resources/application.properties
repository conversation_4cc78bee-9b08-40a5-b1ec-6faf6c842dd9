spring.application.name=PetCareBE
server.port=8081
jwt.secret=${JWT_SECRET}
jwt.expiration=3600000
# K?t n?i MySQL
spring.datasource.url=${DATABASE_URL}
spring.datasource.username=${DATABASE_USERNAME}
spring.datasource.password=${DATABASE_PASSWORD}

# Driver MySQL
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA / Hibernate
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
# Gi? nguyên tên c?t/table nh? trong @Column, @Table
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

logging.level.org.springframework.security=DEBUG
logging.level.org.example.petcarebe=DEBUG

# User validation configuration
app.validation.password.min-length=3
app.validation.password.max-length=50
app.validation.username.min-length=3
app.validation.username.max-length=50



# Cloudinary Configuration
cloudinary.cloud_name=dhwkxxjmz
cloudinary.api_key=246221928423169
cloudinary.api_secret=i8GKT-QiaughVKJTAhJSim81ti4


# VNPay Sandbox Configuration
vnpay.url=https://sandbox.vnpayment.vn/paymentv2/vpcpay.html
vnpay.tmnCode=YOUR_TMN_CODE
vnpay.hashSecret=YOUR_HASH_SECRET
vnpay.returnUrl=http://localhost:8081/api/payments/vnpay-return

